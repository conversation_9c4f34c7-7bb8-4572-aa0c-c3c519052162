// convex/example.ts
import { R2 } from "@convex-dev/r2";
import { v } from "convex/values";
import { api, components } from "./_generated/api";
import { DataModel, Id } from "./_generated/dataModel";
import { mutation, query } from "./_generated/server";
import { requireUser } from "./users";
import { DOCS_STATUS, ROLES } from "./utils/constants";
import { generateSlug } from "./helpers/mediaHelpers";

export const r2 = new R2(components.r2);

export const { generateUploadUrl, syncMetadata, deleteObject } =
  r2.clientApi<DataModel>({
    checkUpload: async (ctx, bucket) => {
      // const user = await userFromAuth(ctx);
      // ...validate that the user can upload to this bucket
      const userId = await requireUser(ctx);
      if (!userId) {
        throw new Error("Unauthorized");
      }
    },
    checkDelete: async (ctx, bucket, key) => {
      // const user = await userFromAuth(ctx);
      // ...validate that the user can delete this key
      // can delete if he is media manager or admin
      const userId = await requireUser(ctx);
      if (!userId) {
        throw new Error("Unauthorized");
      }
      const user = await ctx.db.get(userId);
      if (!user) {
        throw new Error("Unauthorized");
      }
      if (user.role !== "media-manager" && user.role !== "admin") {
        throw new Error("Unauthorized");
      }
    },
    onUpload: async (ctx, bucket, key) => {
      // ...do something with the key
      // This technically runs in the `syncMetadata` mutation, as the upload
      // is performed from the client side. Will run if using the `useUploadFile`
      // hook, or if `syncMetadata` function is called directly. Runs after the
      // `checkUpload` callback.
      const userId = await requireUser(ctx);
      const user = await ctx.db.get(userId);
      if (!userId || !user) {
        throw new Error("Unauthorized");
      }
      if (user.role !== ROLES.MEDIA_MANAGER && user.role !== ROLES.ADMIN) {
        throw new Error("Unauthorized");
      }

      // get recent files add 1 to length so title is unique be like image-1
      const recentFiles = await ctx.db.query("mediaFiles").collect();
      const title = `file-${recentFiles.length + 1}`;
      const slug = await generateSlug(ctx, title);

      const imageId = await ctx.db.insert("mediaFiles", {
        bucket,
        key,
        userId,
        status:
          user.role === ROLES.ADMIN ? DOCS_STATUS.APPROVED : DOCS_STATUS.DRAFT,
        title,
        slug,
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "mediaFile",
          docId: imageId,
        },
        docTitle: title,
        docStatus:
          user.role === ROLES.ADMIN ? DOCS_STATUS.APPROVED : DOCS_STATUS.DRAFT,
        action: "created" as const,
      });
    },
    onDelete: async (ctx, bucket, key) => {
      // Delete related data from your database, etc.
      // Runs after the `checkDelete` callback.
      // Alternatively, you could have your own `deleteImage` mutation that calls
      // the r2 component's `deleteObject` function.
      const image = await ctx.db
        .query("mediaFiles")
        .withIndex("bucket_key", (q) => q.eq("bucket", bucket).eq("key", key))
        .unique();
      if (image) {
        await ctx.db.delete(image._id);
        await ctx.runMutation(api.activity.createActivity, {
          target: {
            docType: "mediaFile",
            docId: image._id,
          },
          docTitle: image.title,
          docStatus: image.status,
          action: "deleted" as const,
        });
      }
    },
  });

export const listMediaFiles = query({
  args: {},
  handler: async (ctx) => {
    const files = await ctx.db.query("mediaFiles").collect();
    const metadata = await Promise.all(
      files.map(async (file) => {
        const metadata = await r2.getMetadata(ctx, file.key);
        // i want size and contentType
        return {
          size: metadata ? metadata.size : 0,
          contentType: metadata?.contentType
            ? metadata.contentType.split("/")[0]
            : "image",
        };
      })
    );
    return Promise.all(
      files.map(async (file) => ({
        ...file,
        size: metadata[files.indexOf(file)].size,
        contentType: metadata[files.indexOf(file)].contentType,
        url: await r2.getUrl(file.key),
      }))
    );
  },
});

export const getMediaFile = query({
  args: {
    key: v.string(),
  },
  handler: async (ctx, args) => {
    const image = await ctx.db
      .query("mediaFiles")
      .withIndex("bucket_key", (q) => q.eq("bucket", args.key))
      .unique();
    if (!image) {
      return null;
    }
    const metadata = await r2.getMetadata(ctx, args.key);
    const size = metadata ? metadata.size : 0;
    const contentType = metadata?.contentType
      ? metadata.contentType.split("/")[0]
      : "image";
    const url = await r2.getUrl(args.key);
    return {
      ...image,
      size,
      contentType,
      url,
    };
  },
});

export const getMediaFileBySlug = query({
  args: {
    slug: v.string(),
  },
  handler: async (ctx, args) => {
    const image = await ctx.db
      .query("mediaFiles")
      .withIndex("by_slug", (q) => q.eq("slug", args.slug))
      .unique();
    if (!image) {
      return null;
    }
    const metadata = await r2.getMetadata(ctx, image.key);
    const size = metadata ? metadata.size : 0;
    const contentType = metadata?.contentType
      ? metadata.contentType.split("/")[0]
      : "image";
    const url = await r2.getUrl(image.key);
    return {
      ...image,
      size,
      contentType,
      url,
    };
  },
});

export const searchMediaFile = query({
  args: {
    query: v.string(),
  },
  handler: async (ctx, { query }) => {
    try {
      const images = await ctx.db
        .query("mediaFiles")
        .withSearchIndex("search_title", (q) => q.search("title", query))
        .collect();
      const metadata = await Promise.all(
        images.map(async (image) => {
          const metadata = await r2.getMetadata(ctx, image.key);
          // i want size and contentType
          return {
            size: metadata ? metadata.size : 0,
            contentType: metadata?.contentType
              ? metadata.contentType.split("/")[0]
              : "image",
          };
        })
      );
      return Promise.all(
        images.map(async (image) => ({
          ...image,
          size: metadata[images.indexOf(image)].size,
          contentType: metadata[images.indexOf(image)].contentType,
          url: await r2.getUrl(image.key),
        }))
      );
    } catch {
      return { success: false, error: "Failed to search images." };
    }
  },
});

export const updateMediaFile = mutation({
  args: {
    id: v.id("mediaFiles"),
    title: v.string(),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      const image = await ctx.db.get(args.id);
      if (!image) {
        return { success: false, error: "Image not found." };
      }
      const slug =
        args.title
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, "-")
          .replace(/^-|-$/g, "") || `untitled-${Date.now()}`;
      await ctx.db.patch(args.id, {
        title: args.title,
        description: args.description,
        slug,
        updatedAt: Date.now(),
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "mediaFile",
          docId: args.id,
        },
        docTitle: args.title,
        docStatus: "draft",
        action: "updated" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to update image." };
    }
  },
});

export const deleteMediaFileTemp = mutation({
  args: {
    id: v.id("mediaFiles"),
  },
  handler: async (ctx, args) => {
    try {
      const image = await ctx.db.get(args.id);
      if (!image) {
        return { success: false, error: "Image not found." };
      }
      await ctx.db.patch(image._id, {
        status: "deleted",
        updatedAt: Date.now(),
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "mediaFile",
          docId: args.id,
        },
        docTitle: image.title,
        docStatus: image.status,
        action: "deleted" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to delete image." };
    }
  },
});

export const restoreMediaFile = mutation({
  args: {
    id: v.id("mediaFiles"),
  },
  handler: async (ctx, args) => {
    try {
      const image = await ctx.db.get(args.id);
      if (!image) {
        return { success: false, error: "Image not found." };
      }
      await ctx.db.patch(image._id, {
        status: "draft",
        updatedAt: Date.now(),
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "mediaFile",
          docId: args.id,
        },
        docTitle: image.title,
        docStatus: image.status,
        action: "restored" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to restore image." };
    }
  },
});

export const toggleFavoriteMediaFile = mutation({
  args: {
    id: v.id("mediaFiles"),
  },
  handler: async (ctx, args) => {
    try {
      const image = await ctx.db.get(args.id);
      if (!image) {
        return { success: false, error: "Image not found." };
      }
      await ctx.db.patch(args.id, {
        isFavorite: !image.isFavorite,
        updatedAt: Date.now(),
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "mediaFile",
          docId: args.id,
        },
        docTitle: image.title,
        docStatus: image.status,
        action: image.isFavorite
          ? ("removed-from-favorite" as const)
          : ("added-to-favorite" as const),
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to toggle favorite image." };
    }
  },
});

export const stageMediaFile = mutation({
  args: {
    id: v.id("mediaFiles"),
  },
  handler: async (ctx, args) => {
    try {
      const image = await ctx.db.get(args.id);
      if (!image) {
        return { success: false, error: "Image not found." };
      }
      await ctx.db.patch(image._id, {
        status: "staged",
        updatedAt: Date.now(),
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "mediaFile",
          docId: args.id,
        },
        docTitle: image.title,
        docStatus: image.status,
        action: "staged" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to stage image." };
    }
  },
});

export const unStageMediaFile = mutation({
  args: {
    id: v.id("mediaFiles"),
  },
  handler: async (ctx, args) => {
    try {
      const image = await ctx.db.get(args.id);
      if (!image) {
        return { success: false, error: "Image not found." };
      }
      await ctx.db.patch(image._id, {
        status: "draft",
        updatedAt: Date.now(),
      });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "mediaFile",
          docId: args.id,
        },
        docTitle: image.title,
        docStatus: image.status,
        action: "unstaged" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to unstage image." };
    }
  },
});

export const getMediaFilesAnalytics = query({
  args: {},
  handler: async (ctx) => {
    const userId = await requireUser(ctx);
    const images = await ctx.db
      .query("mediaFiles")
      .withIndex("by_userId", (q) => q.eq("userId", userId))
      .collect();
    return images.map((image) => ({
      date: new Date(image._creationTime).toISOString().split("T")[0], // 'YYYY-MM-DD'
      status: image.status,
    }));
  },
});

export const getTotalMediaFilesByStatus = query({
  args: {
    status: v.union(
      v.literal("draft"),
      v.literal("staged"),
      v.literal("approved"),
      v.literal("published"),
      v.literal("deleted")
    ),
  },
  handler: async (ctx, { status }) => {
    const userId = await requireUser(ctx);
    const total = await ctx.db
      .query("mediaFiles")
      .withIndex("by_status_userId", (q) =>
        q.eq("status", status).eq("userId", userId)
      )
      .collect()
      .then((images) => images.length);

    return total;
  },
});

export const getApprovedMediaFiles = query({
  args: {},
  handler: async (ctx) => {
    try {
      const images = await ctx.db
        .query("mediaFiles")
        .withIndex("by_status", (q) => q.eq("status", "approved"))
        .collect();
      const metadata = await Promise.all(
        images.map(async (image) => {
          const metadata = await r2.getMetadata(ctx, image.key);
          // i want size and contentType
          return {
            size: metadata ? metadata.size : 0,
            contentType: metadata?.contentType
              ? metadata.contentType.split("/")[0]
              : "image",
          };
        })
      );
      return Promise.all(
        images.map(async (image) => ({
          ...image,
          size: metadata[images.indexOf(image)].size,
          contentType: metadata[images.indexOf(image)].contentType,
          url: await r2.getUrl(image.key),
        }))
      );
    } catch {
      return { success: false, error: "Failed to fetch approved images." };
    }
  },
});

export const addMediaCoverToArticle = mutation({
  args: {
    articleId: v.id("articles"),
    mediaId: v.optional(v.id("mediaFiles")),
    externalUrl: v.optional(v.string()),
  },
  handler: async (ctx, { articleId, mediaId, externalUrl }) => {
    try {
      // get image url and add it as cover to article
      if (mediaId) {
        const image = await ctx.db.get(mediaId);
        if (!image) return { success: false, error: "Image not found." };
        const url = await r2.getUrl(image.key);
        await ctx.db.patch(articleId, { coverImage: url });
      }
      if (externalUrl) {
        await ctx.db.patch(articleId, { coverImage: externalUrl });
      }
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: articleId,
        },
        docTitle: "Article",
        docStatus: "approved",
        action: "cover-added" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to add cover to article." };
    }
  },
});

export const removeMediaCoverFromArticle = mutation({
  args: {
    articleId: v.id("articles"),
  },
  handler: async (ctx, { articleId }) => {
    try {
      await ctx.db.patch(articleId, { coverImage: undefined });
      await ctx.runMutation(api.activity.createActivity, {
        target: {
          docType: "article",
          docId: articleId,
        },
        docTitle: "Article",
        docStatus: "approved",
        action: "cover-removed" as const,
      });
      return { success: true };
    } catch {
      return { success: false, error: "Failed to remove cover from article." };
    }
  },
});

// for api i need title , url, description, createdAt
// export const getMediaFilesForApi = query({
//   args: {},
//   handler: async (ctx) => {
//     try {
//       const files = await ctx.db.query("mediaFiles").collect();
//       const metadata = await Promise.all(
//         files.map(async (file) => {
//           const metadata = await r2.getMetadata(ctx, file.key);
//           // i want size and contentType
//           return {
//             size: metadata ? metadata.size : 0,
//             contentType: metadata?.contentType
//               ? metadata.contentType.split("/")[0]
//               : "image",
//           };
//         })
//       );
//       const data = await Promise.all(
//         files.map(async (file) => ({
//           ...file,
//           size: metadata[files.indexOf(file)].size,
//           contentType: metadata[files.indexOf(file)].contentType,
//           url: await r2.getUrl(file.key),
//         }))
//       );
//       return data;
//     } catch {
//       return { success: false, error: "Failed to fetch images." };
//     }
//   },
// });

// get all images only title ,description, url, createdAt
export const getImageFilesForApi = query({
  args: {},
  handler: async (ctx) => {
    try {
      const files = await ctx.db
        .query("mediaFiles")
        .withIndex("by_status", (q) => q.eq("status", "published"))
        .collect();
      const metadata = await Promise.all(
        files.map(async (file) => {
          const metadata = await r2.getMetadata(ctx, file.key);
          // i want size and contentType
          return {
            size: metadata ? metadata.size : 0,
            contentType: metadata?.contentType
              ? metadata.contentType.split("/")[0]
              : "image",
          };
        })
      );

      const data = await Promise.all(
        files.map(async (file) => ({
          ...file,
          size: metadata[files.indexOf(file)].size,
          contentType: metadata[files.indexOf(file)].contentType,
          url: await r2.getUrl(file.key),
        }))
      );
      // images only
      const images = data.filter((file) => file.contentType === "image");
      const imageData = await Promise.all(
        images.map(async (image) => {
          let groupName: string | null = null;
          if (image.groupId) {
            const group = await ctx.db.get(image.groupId as Id<"groups">);
            groupName = group?.name ?? null;
          }

          return {
            title: image.title,
            slug: image.slug,
            description: image.description,
            url: image.url,
            createdAt: image._creationTime,
            group: groupName,
          };
        })
      );
      return {
        success: true,
        data: imageData,
      };
    } catch (error) {
      return { success: false, error: "Failed to fetch images." };
    }
  },
});

// all audios
export const getAudioFilesForApi = query({
  args: {},
  handler: async (ctx) => {
    try {
      const files = await ctx.db
        .query("mediaFiles")
        .withIndex("by_status", (q) => q.eq("status", "published"))
        .collect();
      const metadata = await Promise.all(
        files.map(async (file) => {
          const metadata = await r2.getMetadata(ctx, file.key);
          // i want size and contentType
          return {
            size: metadata ? metadata.size : 0,
            contentType: metadata?.contentType
              ? metadata.contentType.split("/")[0]
              : "image",
          };
        })
      );
      const data = await Promise.all(
        files.map(async (file) => ({
          ...file,
          size: metadata[files.indexOf(file)].size,
          contentType: metadata[files.indexOf(file)].contentType,
          url: await r2.getUrl(file.key),
        }))
      );
      const audios = data.filter((file) => file.contentType === "audio");
      const audioData = audios.map(async (audio) => {
        let groupName: string | null = null;
        if (audio.groupId) {
          const group = await ctx.db.get(audio.groupId as Id<"groups">);
          groupName = group?.name ?? null;
        }
        return {
          title: audio.title,
          slug: audio.slug,
          description: audio.description,
          url: audio.url,
          createdAt: audio._creationTime,
          group: groupName,
        };
      });
      return {
        success: true,
        data: audioData,
      };
    } catch (error) {
      return { success: false, error: "Failed to fetch audios." };
    }
  },
});

// all videos
export const getVideoFilesForApi = query({
  args: {},
  handler: async (ctx) => {
    try {
      const files = await ctx.db
        .query("mediaFiles")
        .withIndex("by_status", (q) => q.eq("status", "published"))
        .collect();
      const metadata = await Promise.all(
        files.map(async (file) => {
          const metadata = await r2.getMetadata(ctx, file.key);
          // i want size and contentType
          return {
            size: metadata ? metadata.size : 0,
            contentType: metadata?.contentType
              ? metadata.contentType.split("/")[0]
              : "image",
          };
        })
      );
      const data = await Promise.all(
        files.map(async (file) => ({
          ...file,
          size: metadata[files.indexOf(file)].size,
          contentType: metadata[files.indexOf(file)].contentType,
          url: await r2.getUrl(file.key),
        }))
      );
      const videos = data.filter((file) => file.contentType === "video");
      const videoData = videos.map(async (video) => {
        let groupName: string | null = null;
        if (video.groupId) {
          const group = await ctx.db.get(video.groupId as Id<"groups">);
          groupName = group?.name ?? null;
        }
        return {
          title: video.title,
          slug: video.slug,
          description: video.description,
          url: video.url,
          createdAt: video._creationTime,
          group: groupName,
        };
      });
      return { success: true, data: videoData };
    } catch (error) {
      return { success: false, error: "Failed to fetch videos." };
    }
  },
});
