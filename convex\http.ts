import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";
import {
  getArticles,
  getAudios,
  getImages,
  getTeamMembers,
  getVideos,
} from "./api";
import { auth } from "./auth";
import { resend } from "./emails";

const http = httpRouter();

auth.addHttpRoutes(http);
http.route({
  path: "/resend-webhook",
  method: "POST",
  handler: httpAction(async (ctx, req) => {
    return await resend.handleResendEventWebhook(ctx, req);
  }),
});

http.route({
  path: "/articles",
  method: "GET",
  handler: getArticles,
});

http.route({
  path: "/members",
  method: "GET",
  handler: getTeamMembers,
});

http.route({
  path: "/images",
  method: "GET",
  handler: getImages,
});

http.route({
  path: "/audios",
  method: "GET",
  handler: getAudios,
});

http.route({
  path: "/videos",
  method: "GET",
  handler: getVideos,
});

export default http;
