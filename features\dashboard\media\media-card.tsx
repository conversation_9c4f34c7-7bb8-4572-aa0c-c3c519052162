import { formatDistanceToNow } from 'date-fns';
import { DatabaseIcon } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@/components/ui/hover-card';
import { formatBytes } from '@/hooks/use-file-upload';
import { cn } from '@/lib/utils';
import { getIconByMediaType, getIconByStatus } from '../shared/icons';
import { MediaCardActions } from './media-card-actions';
import type { TMedia } from './types';

export default function MediaCard({
  media,
  basePath,
}: {
  media: TMedia;
  basePath: string;
}) {
  if (!media || 'success' in media) {
    return null;
  }
  const Icon = getIconByStatus(media.status);
  const MediaIcon = getIconByMediaType(media.contentType);

  return (
    <Card
      className={cn(
        'w-full rounded-sm shadow-xs ring-ring/20 hover:ring dark:bg-background dark:ring-ring/40',
        media.status === 'deleted' &&
          'ring-1 ring-[#f49ca2ca] dark:ring-[#551a1e]'
      )}
      key={media._id}
    >
      <CardHeader>
        <CardTitle className="mr-7 flex items-center gap-2 truncate">
          <MediaIcon className="size-4 shrink-0 text-muted-foreground" />
          <span className="truncate">{media.title || 'Untitled Media'}</span>
        </CardTitle>
        <CardDescription className="mr-10 truncate">
          <HoverCard>
            <HoverCardTrigger asChild>
              <Link
                className="hover:underline"
                href={`${basePath}/media/${media.slug}`}
              >
                better-flow.com/{media.slug}
              </Link>
            </HoverCardTrigger>
            <HoverCardContent align="end" className="ml-10 p-2">
              <AspectRatio className="rounded-lg bg-muted" ratio={16 / 9}>
                {media.url ? (
                  <>
                    {media.contentType === 'image' && (
                      <Image
                        alt="Photo by Drew Beamer"
                        className="h-full w-full rounded-lg object-cover"
                        fill
                        src={media.url}
                      />
                    )}
                    {media.contentType === 'video' && (
                      <video
                        autoPlay
                        className="h-full w-full rounded-lg object-cover"
                        controls
                        loop
                        muted
                        src={media.url}
                      >
                        <track kind="captions" />
                      </video>
                    )}
                    {media.contentType === 'audio' && (
                      <audio
                        className="h-full w-full rounded-lg object-cover"
                        controls
                        controlsList="nodownload"
                        playsInline
                        preload="metadata"
                        src={media.url}
                        title={media.title}
                      >
                        <track kind="captions" />
                      </audio>
                    )}
                    {media.contentType === 'application' && (
                      <div className="flex h-full w-full items-center justify-center text-muted-foreground">
                        <p className="text-sm">No preview available</p>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="flex h-full w-full items-center justify-center text-muted-foreground">
                    <p className="text-sm">No preview available</p>
                  </div>
                )}
              </AspectRatio>
            </HoverCardContent>
          </HoverCard>
        </CardDescription>
        <CardAction>
          <MediaCardActions media={media} />
        </CardAction>
      </CardHeader>
      <CardContent className="flex flex-col gap-2">
        <div className="flex items-center gap-1">
          <Icon className="size-4 text-muted-foreground" />
          <p className="font-medium text-sm capitalize tracking-tight">
            {media.status}
          </p>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1 text-muted-foreground">
            <DatabaseIcon className="size-4" />
            <p className=" text-sm capitalize tracking-tight">
              {formatBytes(media.size || 0)}
            </p>
          </div>

          <p className="text-muted-foreground text-sm">
            {formatDistanceToNow(new Date(media._creationTime), {
              addSuffix: true,
            })}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
