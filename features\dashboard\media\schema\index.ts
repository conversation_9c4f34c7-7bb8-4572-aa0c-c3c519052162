import z from "zod";
// image has  ,description and url
export const mediaSchema = z.object({
  title: z
    .string()
    .min(3, { message: "Title must be at least 3 characters." })
    .max(160, { message: "Title must not be longer than 160 characters." }),
  description: z.string().optional(),
  url: z.string(),
});
export type TMediaSchema = z.infer<typeof mediaSchema>;

export const rejectionMessageSchema = z.object({
  message: z.string().min(1, { message: "Message is required." }),
});
export type TRejectionMessageSchema = z.infer<typeof rejectionMessageSchema>;
