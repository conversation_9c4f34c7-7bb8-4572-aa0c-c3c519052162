export type TArticle = {
  title: string;
  slug: string;
  description: string | undefined;
  content: string | undefined;
  words: number | undefined;
  coverImage: string;
  status: "draft" | "staged" | "approved" | "published" | "deleted";
  group: string;
  authorId: string;
  publishedAt: number;
};

export type TMember = {
  id: string;
  avatarUrl: string | null | undefined;
  _creationTime: number;
  name?: string | undefined;
  image?: string | undefined;
  email?: string | undefined;
  phone?: string | undefined;
  username?: string | undefined;
  bio?: string | undefined;
  role?: "author" | "admin" | "media-manager" | "ads-manager" | undefined;
  coverImage?: string | undefined;
  socialLinks?:
    | {
        name: string;
        url: string;
      }[]
    | undefined;
};

export type TMedia = {
  title: string;
  slug: string;
  description: string | undefined;
  url: string;
  createdAt: number;
  group: string | null;
};
