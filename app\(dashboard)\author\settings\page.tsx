import { convexAuthNextjsToken } from '@convex-dev/auth/nextjs/server';
import { preloadQuery } from 'convex/nextjs';
import type { Metadata } from 'next';
import { api } from '@/convex/_generated/api';
import { AddSocialLinks } from '@/features/dashboard/settings/components/add-social-links';
import { BioCard } from '@/features/dashboard/settings/components/bio-card';
import { CoverImageCard } from '@/features/dashboard/settings/components/cover-image-card';
import DeleteUserCard from '@/features/dashboard/settings/components/delete-account-card';
import { DisplayNameCard } from '@/features/dashboard/settings/components/display-name-card';
import { UserImageCard } from '@/features/dashboard/settings/components/user-image-card';
import { UserNameCard } from '@/features/dashboard/settings/components/user-name-card';

export const metadata: Metadata = {
  title: 'Settings',
  description: 'Settings',
};

export default async function DashboardSettings() {
  const preloadedUser = await preloadQuery(
    api.users.getUser,
    {},
    { token: await convexAuthNextjsToken() }
  );

  if (!preloadedUser) {
    return null;
  }

  return (
    <main className="flex h-full w-full flex-col gap-6">
      {/* Avatar */}
      <UserImageCard preloadedUser={preloadedUser} />

      {/* Display Name */}
      <DisplayNameCard preloadedUser={preloadedUser} />

      {/* Username Name */}
      <UserNameCard preloadedUser={preloadedUser} />

      {/* Bio */}
      <BioCard preloadedUser={preloadedUser} />

      {/* Cover Image */}
      <CoverImageCard preloadedUser={preloadedUser} />

      {/* Social Links */}
      <AddSocialLinks preloadedUser={preloadedUser} />

      {/* Delete Account */}
      <DeleteUserCard />
    </main>
  );
}
