import Image from 'next/image';
import { PageContainer } from '@/components/custom/page-container';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { getImages } from '@/features/root/data';

export default async function ImagesPage() {
  const images = await getImages();
  if (!images) return null;
  return (
    <PageContainer>
      <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3">
        {images.map((image) => (
          <div key={image.title}>
            <h1 className="text-center">{image.title}</h1>
            <p>{image.description}</p>
            <p>{image.group}</p>
            <AspectRatio ratio={16 / 9}>
              <Image
                alt={image.title}
                className="aspect-video w-full rounded-sm object-cover transition-all duration-300 hover:scale-105"
                height={1000}
                src={image.url}
                width={1000}
              />
            </AspectRatio>
          </div>
        ))}
      </div>
    </PageContainer>
  );
}
