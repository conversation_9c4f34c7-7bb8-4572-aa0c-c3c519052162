// import { members } from "@/config/web";
import type { TArticle, TMedia, TMember } from "./types";

export async function getArticles(): Promise<TArticle[]> {
  const res = await fetch(`${process.env.CONVEX_SITE_URL}/articles`, {
    headers: {
      Authorization: `Bearer ${process.env.ARTICLES_API_KEY}`,
    },
  });
  const articles = await res.json();
  if (!articles) return [];
  return articles;
}

export async function getArticle(slug: string): Promise<TArticle | null> {
  // use filter
  const articles = await getArticles();
  const article = articles.find((art) => art.slug === slug);
  if (!article) return null;
  return article;
}

export async function getMembers(): Promise<TMember[]> {
  const res = await fetch(`${process.env.CONVEX_SITE_URL}/members`);
  const teamMembers = res.json();
  if (!teamMembers) return [];
  return teamMembers;
}

export async function getMember(username: string): Promise<TMember | null> {
  const teamMembers = await getMembers();
  const teamMember = teamMembers.find((member) => member.username === username);
  if (!teamMember) return null;
  return teamMember;
}

export async function getMemberById(id: string): Promise<TMember | null> {
  const teamMembers = await getMembers();
  const teamMember = teamMembers.find((member) => member.id === id);
  if (!teamMember) return null;
  return teamMember;
}

// if is media-manager return Media Manager , if is author return Author, if is admin return Admin, if is ads-manager return Ads Manager
export function getRoleName(role: TMember["role"]) {
  if (role === "media-manager") return "Media Manager";
  if (role === "author") return "Author";
  if (role === "admin") return "Admin";
  if (role === "ads-manager") return "Ads Manager";
  return "Unknown";
}

export async function getImages(): Promise<TMedia[]> {
  const res = await fetch(`${process.env.CONVEX_SITE_URL}/images`);
  const images = res.json();
  if (!images) return [];
  return images;
}

export async function getAudios(): Promise<TMedia[]> {
  const res = await fetch(`${process.env.CONVEX_SITE_URL}/audios`);
  const audios = res.json();
  if (!audios) return [];
  return audios;
}

export async function getAudio(slug: string): Promise<TMedia | null> {
  const res = await fetch(`${process.env.CONVEX_SITE_URL}/audios/${slug}`);
  const media = res.json();
  if (!media) return null;
  return media;
}
export async function getVideos(): Promise<TMedia[]> {
  const res = await fetch(`${process.env.CONVEX_SITE_URL}/videos`);
  const videos = res.json();
  if (!videos) return [];
  return videos;
}
export async function getVideo(slug: string): Promise<TMedia | null> {
  const res = await fetch(`${process.env.CONVEX_SITE_URL}/videos/${slug}`);
  const media = res.json();
  if (!media) return null;
  return media;
}
