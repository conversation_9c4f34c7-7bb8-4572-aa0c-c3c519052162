import { api } from "./_generated/api";
import { httpAction } from "./_generated/server";

// get all published articles
export const getArticles = httpAction(async (ctx, req) => {
  // get key from headers
  const authHeader = req.headers.get("Authorization") || "";
  const key = authHeader.replace("Bearer ", "").trim();

  if (key !== process.env.ARTICLES_API_KEY) {
    return new Response(JSON.stringify({ message: "Unauthorized" }), {
      status: 401,
    });
  }
  if (!key) {
    return new Response(JSON.stringify({ message: "Unauthorized" }), {
      status: 401,
    });
  }
  const articles = await ctx.runQuery(api.articles.getPublishedArticles, {});
  return new Response(JSON.stringify(articles), {
    status: 200,
    headers: { "Content-Type": "application/json" },
  });
});

export const getTeamMembers = httpAction(async (ctx, req) => {
  const members = await ctx.runQuery(api.users.getAllUsers, {});
  return new Response(JSON.stringify(members), {
    status: 200,
    headers: { "Content-Type": "application/json" },
  });
});

export const getImages = httpAction(async (ctx, req) => {
  const images = await ctx.runQuery(api.media.getImageFilesForApi, {});

  if (!images.success) {
    return new Response(JSON.stringify(images), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
  if (!images.data) {
    return new Response(JSON.stringify(images), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
  return new Response(JSON.stringify(images.data), {
    status: 200,
    headers: { "Content-Type": "application/json" },
  });
});

export const getAudios = httpAction(async (ctx, req) => {
  const audios = await ctx.runQuery(api.media.getAudioFilesForApi, {});
  if (!audios.success) {
    return new Response(JSON.stringify(audios), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
  if (!audios.data) {
    return new Response(JSON.stringify(audios), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
  return new Response(JSON.stringify(audios.data), {
    status: 200,
    headers: { "Content-Type": "application/json" },
  });
});

export const getVideos = httpAction(async (ctx, req) => {
  const videos = await ctx.runQuery(api.media.getVideoFilesForApi, {});
  if (!videos.success) {
    return new Response(JSON.stringify(videos), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
  if (!videos.data) {
    return new Response(JSON.stringify(videos), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
  return new Response(JSON.stringify(videos.data), {
    status: 200,
    headers: { "Content-Type": "application/json" },
  });
});
