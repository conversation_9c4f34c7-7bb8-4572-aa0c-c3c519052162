'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { type Preloaded, useMutation, usePreloadedQuery } from 'convex/react';
import { TrashIcon } from 'lucide-react';
import Link from 'next/link';
import { useForm } from 'react-hook-form';

import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { api } from '@/convex/_generated/api';
import { getSocialIcon } from '@/lib/utils';
import { socialLinkFormSchema, type TSocialLinkFormValues } from '../schema';

const socialLinks = [
  'twitter',
  'linkedin',
  'github',
  'facebook',
  'instagram',
  'youtube',
  'tiktok',
  'twitch',
  'website',
  'linktree',
];
export function AddSocialLinks({
  preloadedUser,
}: {
  preloadedUser: Preloaded<typeof api.users.getUser>;
}) {
  const user = usePreloadedQuery(preloadedUser);
  const addSocialLink = useMutation(api.users.addSocialLink);
  const removeSocialLink = useMutation(api.users.removeSocialLink);
  const form = useForm<TSocialLinkFormValues>({
    resolver: zodResolver(socialLinkFormSchema),
    defaultValues: { name: '', url: '' },
  });

  if (!user) {
    return null;
  }
  const handleSubmit = (values: TSocialLinkFormValues) => {
    try {
      addSocialLink({ name: values.name, url: values.url });
      toast.success('Social link added successfully!');
      form.reset();
    } catch {
      toast.error('Failed to add social link.');
    }
  };
  const handleRemoveSocialLink = (url: string) => {
    try {
      removeSocialLink({ url });
      toast.success('Social link removed successfully!');
    } catch {
      toast.error('Failed to remove social link.');
    }
  };
  return (
    <Form {...form}>
      <form
        className="flex w-full flex-col items-start rounded-sm ring ring-ring/20 dark:ring-ring/40"
        onSubmit={form.handleSubmit(handleSubmit)}
      >
        <div className="flex w-full flex-col gap-4 rounded-t-sm bg-background p-6 pb-9">
          <div className="flex flex-col gap-2">
            <h2 className="font-medium text-primary text-xl">Social Links</h2>
            <p className="font-normal text-primary/60 text-sm">
              Add your social links. This will be displayed on your profile.
            </p>
          </div>
          <div>
            {user.socialLinks && user.socialLinks.length > 0 && (
              <div className="flex w-1/2 flex-col gap-3 rounded-xl bg-muted/70 p-2">
                {user.socialLinks.map((link) => {
                  const Icon = getSocialIcon(link.name);
                  return (
                    <div
                      className="flex items-center justify-between gap-1 rounded-lg bg-background px-2"
                      key={link.url}
                    >
                      <div className="flex items-center gap-3">
                        <div>{Icon && <Icon className="size-4" />}</div>
                        <Link
                          className="text-muted-foreground hover:underline"
                          href={link.url}
                          rel="noopener noreferrer"
                          target="_blank"
                          title={link.url}
                        >
                          Your {link.name} profile handle
                        </Link>
                      </div>
                      <Button
                        aria-label="Remove social link"
                        className="bg-background hover:text-destructive dark:bg-black"
                        onClick={() => handleRemoveSocialLink(link.url)}
                        size="icon"
                        type="button"
                        variant="ghost"
                      >
                        <TrashIcon className="size-4" />
                      </Button>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
          <div className="flex gap-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="sr-only">Name</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full cursor-pointer dark:bg-background">
                          <SelectValue
                            className="w-full dark:bg-background"
                            placeholder="Select a social link"
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {socialLinks.map((link) => {
                          const Icon = getSocialIcon(link);
                          return (
                            <SelectItem
                              className="cursor-pointer"
                              key={link}
                              value={link}
                            >
                              <div className="flex items-center gap-2">
                                {Icon && <Icon className="size-4" />}
                                {link}
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="sr-only">URL</FormLabel>
                  <FormControl>
                    <Input placeholder="URL" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
        <div className="flex w-full flex-col justify-between gap-3 rounded-lg rounded-t-none border-border border-t bg-secondary px-6 py-3 md:flex-row md:items-center dark:bg-card">
          <p className="font-normal text-primary/60 text-sm">
            Please enter a valid URL.
          </p>
          <Button size="sm" type="submit">
            Save
          </Button>
        </div>
      </form>
    </Form>
  );
}
